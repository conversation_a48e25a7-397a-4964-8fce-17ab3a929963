<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\FarmerInformationModel;
use App\Models\AdxDistrictModel;
use App\Models\AdxLlgModel;
use App\Models\AdxWardModel;
use App\Models\FarmersChildrenModel;
use App\Models\EducationModel;
use App\Models\FarmBlockModel;

class FarmerController extends BaseController
{
    protected $farmerModel;
    protected $childrenModel;
    protected $districtModel;
    protected $llgModel;
    protected $wardModel;
    protected $educationModel;

    public function __construct()
    {
        helper(['form', 'url','info']);
        $this->farmerModel = new FarmerInformationModel();
        $this->childrenModel = new FarmersChildrenModel();
        $this->districtModel = new AdxDistrictModel();
        $this->llgModel = new AdxLlgModel();
        $this->wardModel = new AdxWardModel();
        $this->educationModel = new EducationModel();
    }

    public function index()
    {
        try {
            $farmers = $this->farmerModel->getFarmersWithLocation(
                session()->get('district_id'),
                session()->get('org_id')
            );

            $data = [
                'title' => 'Farmers List',
                'page_header' => 'Farmers Management',
                'farmers' => $farmers
            ];

            return view('staff/farmers/index', $data);

        } catch (\Exception $e) {
            log_message('error', '[Farmers Index] ' . $e->getMessage());

            $data = [
                'title' => 'Farmers List',
                'page_header' => 'Farmers Management',
                'farmers' => []
            ];

            return view('staff/farmers/index', $data)
                ->with('error', 'Error loading farmers list. Please try again.');
        }
    }

    public function create()
    {
        // Get LLGs for the district from session
        $districtId = session()->get('district_id');
        $llgs = $this->llgModel->where('district_id', $districtId)->findAll();

        // Get all wards for the district (through LLGs)
        $llgIds = array_column($llgs, 'id');
        $wards = [];
        if (!empty($llgIds)) {
            $wards = $this->wardModel->whereIn('llg_id', $llgIds)->findAll();
        }

        // Get district name
        $district = $this->districtModel->find($districtId);
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Add New Farmer',
            'page_header' => 'Add New Farmer',
            'llgs' => $llgs,
            'wards' => $wards,
            'district_name' => $districtName,
            'education_levels' => $this->educationModel->findAll()
        ];

        return view('staff/farmers/create', $data);
    }

    public function store()
    {
        try {
            // Debug: Log that store method is being called
            log_message('info', '[Farmer Store] Store method called');

            // Get form data
            $data = $this->request->getPost();
            log_message('info', '[Farmer Store] Form data received: ' . json_encode($data));

            // Validate required session data
            $requiredSessionData = [
                'org_id' => session()->get('org_id'),
                'emp_id' => session()->get('emp_id'),
                'orgprovince_id' => session()->get('orgprovince_id'),
                'district_id' => session()->get('district_id')
            ];
            log_message('info', '[Farmer Store] Session data: ' . json_encode($requiredSessionData));

            foreach ($requiredSessionData as $key => $value) {
                if (empty($value)) {
                    log_message('error', "[Farmer Store] Missing session data: {$key}");
                    return redirect()->back()->withInput()
                        ->with('error', 'Session data missing. Please log in again.');
                }
            }

            // Set system fields
            $data['org_id'] = $requiredSessionData['org_id'];
            $data['created_by'] = $requiredSessionData['emp_id'];
            $data['province_id'] = $requiredSessionData['orgprovince_id'];
            $data['district_id'] = $requiredSessionData['district_id'];
            $data['country_id'] = 1; // Default to PNG
            $data['id_photo'] = ''; // Default empty string for NOT NULL constraint

            // Handle photo upload with validation
            $photo = $this->request->getFile('id_photo');
            if ($photo && $photo->isValid() && !$photo->hasMoved()) {
                // Validate file type and size
                $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                $maxSize = 2 * 1024 * 1024; // 2MB

                if (!in_array($photo->getMimeType(), $allowedTypes)) {
                    return redirect()->back()->withInput()
                        ->with('error', 'Invalid file type. Only JPEG, PNG, and GIF files are allowed.');
                }

                if ($photo->getSize() > $maxSize) {
                    return redirect()->back()->withInput()
                        ->with('error', 'File size too large. Maximum size is 2MB.');
                }

                // Create upload directory if it doesn't exist
                $uploadPath = FCPATH . 'public/uploads/farmer_photos';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }

                $newName = $photo->getRandomName();
                if ($photo->move($uploadPath, $newName)) {
                    $data['id_photo'] = 'public/uploads/farmer_photos/' . $newName;
                } else {
                    return redirect()->back()->withInput()
                        ->with('error', 'Failed to upload photo. Please try again.');
                }
            }

            // Validate and insert data
            if ($this->farmerModel->insert($data)) {
                return redirect()->to('staff/farmers')
                    ->with('success', 'Farmer added successfully');
            } else {
                // Get validation errors
                $errors = $this->farmerModel->errors();
                $errorMessage = 'Failed to add farmer. ';
                if (!empty($errors)) {
                    $errorMessage .= implode(', ', $errors);
                    log_message('error', '[Farmer Store] Validation errors: ' . json_encode($errors));
                } else {
                    log_message('error', '[Farmer Store] Insert failed without validation errors');
                }

                return redirect()->back()->withInput()
                    ->with('error', $errorMessage)
                    ->with('validation_errors', $errors);
            }

        } catch (\Exception $e) {
            log_message('error', '[Farmer Store] ' . $e->getMessage());
            return redirect()->back()->withInput()
                ->with('error', 'An error occurred while adding the farmer. Please try again.');
        }
    }

    public function edit($id)
    {
        $farmer = $this->farmerModel->find($id);
        
        if (!$farmer || $farmer['org_id'] != session()->get('org_id')) {
            return redirect()->to('staff/farmers')->with('error', 'Farmer not found');
        }

        // Get district name
        $district = $this->districtModel->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';
        
        // Get LLGs for the current district
        $llgs = $this->llgModel->where('district_id', session()->get('district_id'))->findAll();

        // Get all wards for the district (through LLGs)
        $llgIds = array_column($llgs, 'id');
        $wards = [];
        if (!empty($llgIds)) {
            $wards = $this->wardModel->whereIn('llg_id', $llgIds)->findAll();
        }

        $data = [
            'title' => 'Edit Farmer',
            'page_header' => 'Edit Farmer',
            'farmer' => $farmer,
            'children' => $this->childrenModel->where('farmer_id', $id)->findAll(),
            'district_name' => $districtName,
            'llgs' => $llgs,
            'wards' => $wards,
            'education_levels' => $this->educationModel->findAll()
        ];

        return view('staff/farmers/edit', $data);
    }

    public function update($id)
    {
        try {
            // Check if farmer exists and belongs to current organization
            $farmer = $this->farmerModel->find($id);
            if (!$farmer || $farmer['org_id'] != session()->get('org_id')) {
                return redirect()->to('staff/farmers')
                    ->with('error', 'Farmer not found or access denied');
            }

            // Get form data
            $data = $this->request->getPost();

            // Set system fields
            $data['updated_by'] = session()->get('emp_id');
            $data['province_id'] = session()->get('orgprovince_id');

            // Handle photo upload with validation
            $photo = $this->request->getFile('id_photo');
            if ($photo && $photo->isValid() && !$photo->hasMoved()) {
                // Validate file type and size
                $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                $maxSize = 2 * 1024 * 1024; // 2MB

                if (!in_array($photo->getMimeType(), $allowedTypes)) {
                    return redirect()->back()->withInput()
                        ->with('error', 'Invalid file type. Only JPEG, PNG, and GIF files are allowed.');
                }

                if ($photo->getSize() > $maxSize) {
                    return redirect()->back()->withInput()
                        ->with('error', 'File size too large. Maximum size is 2MB.');
                }

                // Delete old photo if exists
                if (!empty($farmer['id_photo']) && file_exists(FCPATH . $farmer['id_photo'])) {
                    unlink(FCPATH . $farmer['id_photo']);
                }

                // Create upload directory if it doesn't exist
                $uploadPath = FCPATH . 'public/uploads/farmer_photos';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }

                $newName = $photo->getRandomName();
                if ($photo->move($uploadPath, $newName)) {
                    $data['id_photo'] = 'public/uploads/farmer_photos/' . $newName;
                } else {
                    return redirect()->back()->withInput()
                        ->with('error', 'Failed to upload photo. Please try again.');
                }
            }

            // Validate and update data
            if ($this->farmerModel->update($id, $data)) {
                return redirect()->back()
                    ->with('success', 'Farmer updated successfully');
            } else {
                // Get validation errors
                $errors = $this->farmerModel->errors();
                $errorMessage = 'Failed to update farmer. ';
                if (!empty($errors)) {
                    $errorMessage .= implode(', ', $errors);
                }

                return redirect()->back()->withInput()
                    ->with('error', $errorMessage)
                    ->with('validation_errors', $errors);
            }

        } catch (\Exception $e) {
            log_message('error', '[Farmer Update] ' . $e->getMessage());
            return redirect()->back()->withInput()
                ->with('error', 'An error occurred while updating the farmer. Please try again.');
        }
    }

    public function delete($id)
    {
        try {
            // Check if farmer exists and belongs to current organization
            $farmer = $this->farmerModel->find($id);

            if (!$farmer || $farmer['org_id'] != session()->get('org_id')) {
                return redirect()->to('staff/farmers')
                    ->with('error', 'Farmer not found or access denied');
            }

            // Check if farmer has associated data (farm blocks, etc.)
            // This prevents deletion of farmers with existing farm data
            $farmBlockModel = new \App\Models\FarmBlockModel();
            $farmBlocks = $farmBlockModel->where('farmer_id', $id)->countAllResults();

            if ($farmBlocks > 0) {
                return redirect()->to('staff/farmers')
                    ->with('error', 'Cannot delete farmer. Farmer has associated farm blocks. Please remove farm blocks first.');
            }

            // Delete associated photo if exists
            if (!empty($farmer['id_photo']) && file_exists(FCPATH . $farmer['id_photo'])) {
                unlink(FCPATH . $farmer['id_photo']);
            }

            // Delete farmer record
            if ($this->farmerModel->delete($id)) {
                return redirect()->to('staff/farmers')
                    ->with('success', 'Farmer deleted successfully');
            } else {
                return redirect()->back()
                    ->with('error', 'Failed to delete farmer. Please try again.');
            }

        } catch (\Exception $e) {
            log_message('error', '[Farmer Delete] ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'An error occurred while deleting the farmer. Please try again.');
        }
    }

    /**
     * Soft delete farmer (set status to inactive instead of hard delete)
     */
    public function deactivate($id)
    {
        try {
            $farmer = $this->farmerModel->find($id);

            if (!$farmer || $farmer['org_id'] != session()->get('org_id')) {
                return redirect()->to('staff/farmers')
                    ->with('error', 'Farmer not found or access denied');
            }

            $data = [
                'status' => 'inactive',
                'updated_by' => session()->get('emp_id')
            ];

            if ($this->farmerModel->update($id, $data)) {
                return redirect()->to('staff/farmers')
                    ->with('success', 'Farmer deactivated successfully');
            } else {
                return redirect()->back()
                    ->with('error', 'Failed to deactivate farmer');
            }

        } catch (\Exception $e) {
            log_message('error', '[Farmer Deactivate] ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'An error occurred while deactivating the farmer');
        }
    }

    /**
     * Reactivate farmer
     */
    public function activate($id)
    {
        try {
            $farmer = $this->farmerModel->find($id);

            if (!$farmer || $farmer['org_id'] != session()->get('org_id')) {
                return redirect()->to('staff/farmers')
                    ->with('error', 'Farmer not found or access denied');
            }

            $data = [
                'status' => 'active',
                'updated_by' => session()->get('emp_id')
            ];

            if ($this->farmerModel->update($id, $data)) {
                return redirect()->to('staff/farmers')
                    ->with('success', 'Farmer activated successfully');
            } else {
                return redirect()->back()
                    ->with('error', 'Failed to activate farmer');
            }

        } catch (\Exception $e) {
            log_message('error', '[Farmer Activate] ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'An error occurred while activating the farmer');
        }
    }

    public function view($id)
    {
        $farmer = $this->farmerModel->find($id);
        
        if (!$farmer || $farmer['org_id'] != session()->get('org_id')) {
            return redirect()->to('staff/farmers')->with('error', 'Farmer not found');
        }

        $data = [
            'title' => 'Farmer Details',
            'page_header' => 'Farmer Details',
            'farmer' => $farmer,
            'children' => $this->childrenModel->where('farmer_id', $id)->findAll()
        ];

        return view('staff/farmers/view', $data);
    }









    public function addChild()
    {
        $data = $this->request->getPost();
        
        if ($this->childrenModel->insert($data)) {
            return redirect()->back()->with('success', 'Child added successfully');
        }
        
        return redirect()->back()->withInput()->with('error', 'Failed to add child');
    }

    public function updateChild()
    {
        $id = $this->request->getPost('id');
        $data = $this->request->getPost();
        $data['updated_by'] = session()->get('emp_id');
        if ($this->childrenModel->update($id, $data)) {
            return redirect()->back()->with('success', 'Child updated successfully');
        }
        
        return redirect()->back()->withInput()->with('error', 'Failed to update child');
    }

    public function deleteChild($id)
    {
        if ($this->childrenModel->delete($id)) {
            return redirect()->back()->with('success', 'Child deleted successfully');
        }
        
        return redirect()->back()->with('error', 'Failed to delete child');
    }
}
