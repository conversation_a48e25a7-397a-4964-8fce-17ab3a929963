<?php

namespace App\Models;

use CodeIgniter\Model;

class FarmerInformationModel extends Model
{
    protected $table            = 'farmer_information';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'org_id',
        'farmer_code',
        'given_name',
        'surname',
        'date_of_birth',
        'gender',
        'village',
        'ward_id',
        'llg_id',
        'district_id',
        'province_id',
        'country_id',
        'phone',
        'email',
        'address',
        'marital_status',
        'highest_education_id',
        'course_taken',
        'id_photo',
        'created_by',
        'updated_by',
        'status'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'given_name' => 'required|min_length[2]|max_length[50]|alpha_space',
        'surname' => 'required|min_length[2]|max_length[50]|alpha_space',
        'date_of_birth' => 'required|valid_date|before_date[today]',
        'gender' => 'required|in_list[Male,Female]',
        'village' => 'permit_empty|max_length[100]',
        'ward_id' => 'permit_empty|numeric',
        'llg_id' => 'permit_empty|numeric',
        'district_id' => 'required|numeric',
        'province_id' => 'required|numeric',
        'country_id' => 'permit_empty|numeric',
        'phone' => 'permit_empty|max_length[200]|regex_match[/^[\+]?[0-9\s\-\(\)]+$/]',
        'email' => 'permit_empty|valid_email|max_length[255]',
        'address' => 'permit_empty|max_length[255]',
        'marital_status' => 'required|in_list[Single,Married,Divorce,Widow,De-facto]',
        'highest_education_id' => 'permit_empty|numeric',
        'course_taken' => 'permit_empty|max_length[255]',
        'id_photo' => 'permit_empty|max_length[255]',
        'status' => 'permit_empty|in_list[active,inactive]'
    ];

    protected $validationMessages = [
        'given_name' => [
            'required' => 'Given name is required',
            'min_length' => 'Given name must be at least 2 characters long',
            'max_length' => 'Given name cannot exceed 50 characters',
            'alpha_space' => 'Given name can only contain letters and spaces'
        ],
        'surname' => [
            'required' => 'Surname is required',
            'min_length' => 'Surname must be at least 2 characters long',
            'max_length' => 'Surname cannot exceed 50 characters',
            'alpha_space' => 'Surname can only contain letters and spaces'
        ],
        'date_of_birth' => [
            'required' => 'Date of birth is required',
            'valid_date' => 'Please enter a valid date',
            'before_date' => 'Date of birth must be before today'
        ],
        'gender' => [
            'required' => 'Gender is required',
            'in_list' => 'Gender must be Male or Female'
        ],
        'village' => [
            'max_length' => 'Village name cannot exceed 100 characters'
        ],
        'ward_id' => [
            'numeric' => 'Ward ID must be a number'
        ],
        'llg_id' => [
            'numeric' => 'LLG ID must be a number'
        ],
        'district_id' => [
            'required' => 'District is required',
            'numeric' => 'District ID must be a number'
        ],
        'province_id' => [
            'required' => 'Province is required',
            'numeric' => 'Province ID must be a number'
        ],
        'phone' => [
            'max_length' => 'Phone number cannot exceed 200 characters',
            'regex_match' => 'Please enter a valid phone number'
        ],
        'email' => [
            'valid_email' => 'Please enter a valid email address',
            'max_length' => 'Email cannot exceed 255 characters'
        ],
        'address' => [
            'max_length' => 'Address cannot exceed 255 characters'
        ],
        'marital_status' => [
            'required' => 'Marital status is required',
            'in_list' => 'Please select a valid marital status'
        ],
        'highest_education_id' => [
            'numeric' => 'Education level must be a number'
        ],
        'course_taken' => [
            'max_length' => 'Course taken cannot exceed 255 characters'
        ],
        'status' => [
            'in_list' => 'Status must be active or inactive'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['generateUniqueFarmerCode', 'setDefaultStatus'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    protected function generateUniqueFarmerCode(array $data)
    {
        if (!isset($data['data']['farmer_code']) || empty($data['data']['farmer_code'])) {
            do {
                $code = 'F7' . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
                $exists = $this->where('farmer_code', $code)->first();
            } while ($exists);

            $data['data']['farmer_code'] = $code;
        }
        return $data;
    }

    protected function setDefaultStatus(array $data)
    {
        if (!isset($data['data']['status']) || empty($data['data']['status'])) {
            $data['data']['status'] = 'active';
        }
        return $data;
    }

    public function getFullName($id)
    {
        $farmer = $this->find($id);
        if ($farmer) {
            return $farmer['given_name'] . ' ' . $farmer['surname'];
        }
        return '';
    }

    /**
     * Get farmers with location details
     */
    public function getFarmersWithLocation($districtId = null, $orgId = null)
    {
        $builder = $this->select('farmer_information.*,
                                 adx_district.name as district_name,
                                 adx_llg.name as llg_name,
                                 adx_ward.name as ward_name')
                        ->join('adx_district', 'adx_district.id = farmer_information.district_id', 'left')
                        ->join('adx_llg', 'adx_llg.id = farmer_information.llg_id', 'left')
                        ->join('adx_ward', 'adx_ward.id = farmer_information.ward_id', 'left');

        if ($districtId) {
            $builder->where('farmer_information.district_id', $districtId);
        }

        if ($orgId) {
            $builder->where('farmer_information.org_id', $orgId);
        }

        return $builder->orderBy('farmer_information.created_at', 'DESC')->findAll();
    }
}
